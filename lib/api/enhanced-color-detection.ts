/**
 * Enhanced PDF color detection implementation
 * Based on the standalone script with improvements for handling pages with large white spaces
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import * as os from 'os';

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
  confidence?: string;
  method?: string;
}

/**
 * Detects if a PDF has color and which pages contain color
 * Uses ImageMagick for high-accuracy pixel-by-pixel RGB analysis
 *
 * @param pdfBuffer The PDF file as a buffer
 * @returns Promise resolving to color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  try {
    // First try to import the fallback implementation that uses PDF.js
    try {
      const { detectColor: detectColorFallback } = require('./color-detection-fallback');

      // Try the PDF.js-based implementation first
      try {
        console.log("Attempting color detection with PDF.js fallback implementation...");
        const result = await detectColorFallback(pdfBuffer);
        console.log("PDF.js color detection successful:", result);
        return {
          ...result,
          confidence: 'medium',
          method: 'PDF.js Operator Analysis'
        };
      } catch (pdfJsError) {
        console.error("PDF.js color detection failed, falling back to ImageMagick:", pdfJsError);
        // Continue with ImageMagick implementation
      }
    } catch (importError) {
      console.log("PDF.js fallback implementation not available:", importError.message);
      // Continue with ImageMagick implementation
    }

    // Create a temporary directory for processing
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'pdf-color-'));
    const tempPdfPath = path.join(tempDir, 'document.pdf');

    // Write the buffer to a temporary file
    fs.writeFileSync(tempPdfPath, pdfBuffer);

    // Detect ImageMagick version and capabilities
    let usingImageMagickV7 = false;
    let convertCommandWorks = false;
    let magickCommandWorks = false;

    // First check if 'convert' command works
    try {
      const versionOutput = execSync('convert -version', { encoding: 'utf8' });
      convertCommandWorks = true;

      if (versionOutput.includes('ImageMagick 7')) {
        usingImageMagickV7 = true;
      }
    } catch (error) {
      console.log("'convert' command not available or failed:", error.message);
    }

    // Then check if 'magick' command works
    try {
      const versionOutput = execSync('magick -version', { encoding: 'utf8' });
      magickCommandWorks = true;
      usingImageMagickV7 = true;
    } catch (error) {
      console.log("'magick' command not available or failed:", error.message);
    }

    // Log the detection results
    console.log(`ImageMagick detection results: convert=${convertCommandWorks}, magick=${magickCommandWorks}, v7=${usingImageMagickV7}`);

    // If neither command works, we can't proceed with ImageMagick-based detection
    if (!convertCommandWorks && !magickCommandWorks) {
      console.error("No working ImageMagick commands found. Color detection may not work properly.");
    }

    // Decide which command to use based on availability
    if (usingImageMagickV7 && magickCommandWorks) {
    } else if (convertCommandWorks) {
      usingImageMagickV7 = false;
    } else if (magickCommandWorks) {
      usingImageMagickV7 = true;
    }

    // Extract images from the PDF with multiple fallback methods
    let extractionSuccess = false;
    let extractionError = null;

    // Method 1: Try ImageMagick with appropriate command based on detection
    try {
      if (usingImageMagickV7 && magickCommandWorks) {
        execSync(`magick -density 72 "${tempPdfPath}" -quality 90 "${tempDir}/page-%d.jpg"`, { stdio: 'pipe' });
        extractionSuccess = true;
      } else if (convertCommandWorks) {
        execSync(`convert -density 72 "${tempPdfPath}" -quality 90 "${tempDir}/page-%d.jpg"`, { stdio: 'pipe' });
        extractionSuccess = true;
      }
    } catch (error) {
      extractionError = error;
      console.error('Error with primary extraction method:', error.message);
    }

    // Method 2: If first method failed, try with Ghostscript directly
    if (!extractionSuccess) {
      try {
        console.log("Attempting PDF extraction with Ghostscript directly...");
        // Check if gs command is available
        execSync('gs --version', { stdio: 'pipe' });

        // Use Ghostscript to convert PDF to images
        execSync(`gs -dSAFER -dBATCH -dNOPAUSE -sDEVICE=jpeg -r72 -dTextAlphaBits=4 -dGraphicsAlphaBits=4 -dJPEGQ=90 -sOutputFile="${tempDir}/page-%d.jpg" "${tempPdfPath}"`, { stdio: 'pipe' });
        extractionSuccess = true;
      } catch (gsError) {
        console.error('Error with Ghostscript extraction:', gsError.message);
      }
    }

    // Method 3: If both methods failed, try with pdftoppm if available
    if (!extractionSuccess) {
      try {
        console.log("Attempting PDF extraction with pdftoppm...");
        // Check if pdftoppm command is available
        execSync('pdftoppm -v', { stdio: 'pipe' });

        // Use pdftoppm to convert PDF to images
        execSync(`pdftoppm -jpeg -r 72 "${tempPdfPath}" "${tempDir}/page"`, { stdio: 'pipe' });

        // pdftoppm creates files with a different naming pattern, so rename them to match expected pattern
        const ppmFiles = fs.readdirSync(tempDir).filter(file => file.startsWith('page-') && file.endsWith('.jpg'));
        if (ppmFiles.length === 0) {
          // Rename files from pdftoppm format (page-01.jpg) to our expected format (page-0.jpg)
          const files = fs.readdirSync(tempDir).filter(file => file.endsWith('.jpg'));
          files.forEach(file => {
            const match = file.match(/page-(\d+)\.jpg/);
            if (match) {
              const pageNum = parseInt(match[1]) - 1; // Convert to 0-indexed
              fs.renameSync(
                path.join(tempDir, file),
                path.join(tempDir, `page-${pageNum}.jpg`)
              );
            }
          });
        }
        extractionSuccess = true;
      } catch (pdfToPpmError) {
        console.error('Error with pdftoppm extraction:', pdfToPpmError.message);
      }
    }

    // If all extraction methods failed, return default result
    if (!extractionSuccess) {
      console.error('All PDF extraction methods failed');
      // Clean up
      try {
        fs.rmSync(tempDir, { recursive: true, force: true });
      } catch (cleanupError) {
        console.error('Error cleaning up temporary directory:', cleanupError);
      }

      // Return a default result
      return {
        is_colored: false,
        color_pages: [],
        total_pages: 0,
        confidence: 'low',
        method: 'Failed extraction'
      };
    }

    // Get all extracted images
    const files = fs.readdirSync(tempDir)
      .filter(file => file.startsWith('page-') && file.endsWith('.jpg'))
      .sort((a, b) => {
        const pageA = parseInt(a.match(/page-(\d+)/)?.[1] || '0');
        const pageB = parseInt(b.match(/page-(\d+)/)?.[1] || '0');
        return pageA - pageB;
      });

    const colorPages: number[] = [];

    // Analyze each page
    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // Extract page number from filename
      // Note: ImageMagick's page numbering starts from 0 in the filename (page-0.jpg is the first page)
      // but we want to report 1-indexed page numbers to the user
      const rawPageNum = parseInt(file.match(/page-(\d+)/)?.[1] || '0');
      const pageNum = rawPageNum + 1; // Convert to 1-indexed

      // Get file stats for all pages (for logging and fallback)
      const fileStats = fs.statSync(path.join(tempDir, file));
      const fileSizeKB = fileStats.size / 1024;

      // Check if the page has any non-white content
      let hasNonWhiteContent = false;
      try {
        // This command checks if there's any non-white content (with 1% fuzz factor)
        const contentCheckCmd = usingImageMagickV7
          ? `magick "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`
          : `convert "${path.join(tempDir, file)}" -colorspace RGB -fill white -fuzz 1% -opaque white -fill black -opaque black -format "%[fx:mean]" info:`;

        const contentValue = parseFloat(execSync(contentCheckCmd, { encoding: 'utf8' }).trim());
        hasNonWhiteContent = contentValue > 0;
      } catch (error) {
        // Assume there is content if the check fails
        hasNonWhiteContent = true;
      }

      // Try different approaches to get pixel data
      let pixelData = '';
      let pixelDataSuccess = false;

      // Approach 1: Command with trim to remove white borders and then sampling
      try {
        const pixelDataCmd = usingImageMagickV7
          ? `magick "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 800x800 txt:- | grep -v "^#" | awk 'NR % 10 == 0' | head -n 10000`
          : `convert "${path.join(tempDir, file)}" -colorspace RGB -trim +repage -sample 800x800 txt:- | grep -v "^#" | awk 'NR % 10 == 0' | head -n 10000`;

        pixelData = execSync(pixelDataCmd, { encoding: 'utf8', maxBuffer: 10 * 1024 * 1024 });
        pixelDataSuccess = pixelData.trim().length > 0;
      } catch (error) {
        console.error(`Error with pixel data extraction for page ${pageNum}:`, error);
      }

      // If we found non-white content but couldn't get pixel data, create synthetic pixel data
      if (hasNonWhiteContent && !pixelDataSuccess) {
        // Create synthetic pixel data for a few pixels to ensure analysis continues
        pixelData = "0,0: (240,240,240) #F0F0F0 gray(240)\n1,0: (240,240,240) #F0F0F0 gray(240)\n";
        pixelDataSuccess = true;
      }

      // Parse the pixel data
      const pixelLines = pixelData.trim().split('\n');
      let totalPixels = 0;
      let colorPixels = 0;

      // Process each pixel
      for (const line of pixelLines) {
        // Each line looks like: "0,0: (255,255,255) #FFFFFF white"
        const match = line.match(/\((\d+),(\d+),(\d+)\)/);
        if (match) {
          const r = parseInt(match[1]);
          const g = parseInt(match[2]);
          const b = parseInt(match[3]);

          // Skip pure white or pure black pixels
          if ((r === 255 && g === 255 && b === 255) || (r === 0 && g === 0 && b === 0)) {
            continue;
          }

          totalPixels++;

          // Calculate the average of RGB components
          const avg = (r + g + b) / 3;

          // Calculate the threshold based on the average (1% of the average)
          // Use a minimum threshold to avoid false positives in very dark areas
          const threshold = Math.max(2.55, avg * 0.01); // At least 1% of 255

          // Check if the pixel is non-grayscale using two methods:

          // Method 1: Check if any component differs from the average by more than the threshold
          const isColorByAvg =
            Math.abs(r - avg) > threshold ||
            Math.abs(g - avg) > threshold ||
            Math.abs(b - avg) > threshold;

          // Method 2: Check if the components differ from each other by more than the threshold
          const isColorByDiff =
            Math.abs(r - g) > threshold ||
            Math.abs(r - b) > threshold ||
            Math.abs(g - b) > threshold;

          // A pixel is considered colored if either method detects color
          if (isColorByAvg || isColorByDiff) {
            colorPixels++;
          }
        }
      }

      // Calculate the percentage of color pixels
      const colorPercentage = totalPixels > 0 ? (colorPixels / totalPixels) * 100 : 0;

      // Handle the case where no pixels were analyzed
      if (totalPixels === 0) {
        console.log(`Warning: No pixels analyzed for page ${pageNum}. Using alternative detection methods.`);

        // Method 1: Check if the file size is significant
        if (fileSizeKB > 20) {
          // Method 2: Try to detect color using ImageMagick's identify command
          try {
            // This command checks if the image has a colorspace other than Gray
            const colorspaceCmd = usingImageMagickV7
              ? `magick identify -format "%[colorspace]" "${path.join(tempDir, file)}"`
              : `identify -format "%[colorspace]" "${path.join(tempDir, file)}"`;

            const colorspace = execSync(colorspaceCmd, { encoding: 'utf8' }).trim();

            // If the colorspace is not Gray/Grayscale, it might be a color image
            if (colorspace !== 'Gray' && colorspace !== 'Grayscale' && colorspace !== 'sRGB') {
              colorPages.push(pageNum);
              continue;
            }
          } catch (error) {
            console.error(`Error checking colorspace for page ${pageNum}:`, error);
          }

          // If the file is significantly large (>100KB), it's more likely to contain color
          if (fileSizeKB > 100) {
            colorPages.push(pageNum);
            continue;
          }
        }
      }

      // A page is considered colored if:
      // 1. More than 0.5% of the sampled pixels are colored, AND
      // 2. We have at least 5 colored pixels (to avoid false positives from noise)
      const isColored = colorPercentage > 0.5 && colorPixels >= 5;

      if (isColored) {
        colorPages.push(pageNum);
      }
    }

    // Clean up
    try {
      fs.rmSync(tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
      console.error('Error cleaning up temporary directory:', cleanupError);
    }

    // Return the results
    return {
      is_colored: colorPages.length > 0,
      color_pages: colorPages,
      total_pages: files.length,
      confidence: 'high',
      method: 'Pixel-by-Pixel RGB Analysis'
    };
  } catch (error) {
    console.error('Error in color detection:', error);

    // Return a default result if analysis fails
    return {
      is_colored: false,
      color_pages: [],
      total_pages: 0,
      confidence: 'low',
      method: 'Error'
    };
  }
}
