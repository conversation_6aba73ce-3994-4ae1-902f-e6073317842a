import { NextRequest, NextResponse } from 'next/server';
import { InvoiceData } from '@/lib/types/invoice';
import { processInvoice } from '@/lib/api/external-service';
import { ColorDetectionResult } from '@/lib/api/enhanced-color-detection';
import { validateApi<PERSON>ey, ApiKeyValidationResult } from '@/lib/api/api-key-middleware';
import {
  createErrorResponse,
  createUrlProcessingLog,
  handleColorDetection,
  updateInvoiceLogWithResults,
  updateLogWithError,
  createApiResponse,
  updateLogWithApiKeyId
} from '@/lib/api/shared-invoice-processing';

/**
 * Fetches a PDF from a URL and returns it as a buffer
 * @param url The URL of the PDF file
 * @returns A buffer containing the PDF data
 */
async function fetchPdfFromUrl(url: string): Promise<Buffer> {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch PDF from URL: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error fetching PDF from URL:', error);
    }
    throw error;
  }
}

/**
 * Process and match a standard invoice from a URL
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  // Validate API key
  const validation = await validateApiKey(req);

  // Handle both old and new API key middleware behavior
  if (validation === null) {
    // Old behavior - null means valid
    return NextResponse.json({ results: { success: true } });
  } else if (validation && 'error' in validation && validation.error) {
    // New behavior - error property means invalid
    return validation.error;
  }

  // New behavior - valid with API key ID or empty object for session auth

  try {
    // Parse the JSON body
    const body = await req.json();

    // Parse and coerce types before validation
    const parsedBody = {
      ...body,
      invoice_amount: typeof body.invoice_amount === 'string' ? parseFloat(body.invoice_amount) : body.invoice_amount,
      vat_amount: typeof body.vat_amount === 'string' ? parseFloat(body.vat_amount) : body.vat_amount,
    };

    // Validate required parameters using shared utility
    const requiredFields: Array<{ key: string; name: string; type: 'string' | 'number' | 'date' }> = [
      { key: 'file_url', name: 'file_url', type: 'string' },
      { key: 'vendor_name', name: 'vendor_name', type: 'string' },
      { key: 'invoice_number', name: 'invoice_number', type: 'string' },
      { key: 'invoice_date', name: 'invoice_date', type: 'date' },
      { key: 'invoice_amount', name: 'invoice_amount', type: 'number' },
    ];
    const { validateInvoiceFields } = await import('@/lib/api/validate-invoice-fields');
    const validationError = validateInvoiceFields(parsedBody, requiredFields);
    if (validationError) {
      return NextResponse.json(
        {
          error: validationError,
          results: { success: false }
        },
        { status: 400 }
      );
    }

    // Extract input data from request body
    const fileUrl = body.file_url;
    const inputData: InvoiceData = {
      vendorName: body.vendor_name,
      invoiceNo: body.invoice_number,
      invoiceDate: body.invoice_date,
      invoiceAmount: parseFloat(body.invoice_amount),
      vatAmount: body.vat_amount ? parseFloat(body.vat_amount) : undefined,
    };

    // Create a log entry for this request
    const log = await createUrlProcessingLog(
      'invoice',
      fileUrl,
      inputData
    );

    // If we have an API key ID, update the log with it
    if (validation && 'apiKeyId' in validation && validation.apiKeyId) {
      await updateLogWithApiKeyId(log.id, validation.apiKeyId);
    }

    try {
      // Start the invoice processing
      const externalResponsePromise = processInvoice(fileUrl, inputData);

      // Try to fetch the PDF and detect colors, but don't let it fail the whole request
      let pdfBuffer: Buffer | null = null;
      let colorDetection: ColorDetectionResult = {
        is_colored: false,
        color_pages: [],
        total_pages: 0
      };

      try {
        // Fetch the PDF from the URL
        pdfBuffer = await fetchPdfFromUrl(fileUrl);

        // Detect if the invoice is colored and which pages have color
        if (pdfBuffer) {
          colorDetection = await handleColorDetection(pdfBuffer);
        }
      } catch (colorError) {
        if (process.env.NODE_ENV !== 'test') {
          console.error('Error in PDF fetching or color detection:', colorError);
        }
        // Continue with default values for color detection
      }

      // Wait for the invoice processing to complete
      const externalResponse = await externalResponsePromise;

      // Use shared matching utility for robust field comparison
      const { matchFields } = await import('@/lib/api/field-matching');
      const fieldTypes: Record<string, "string" | "number" | "date"> = {
        vendor_name: 'string',
        invoice_number: 'string',
        invoice_date: 'date',
        invoice_amount: 'number',
        vat_amount: 'number',
      };
      const { fields: enhancedFields, summary: enhancedSummary } = matchFields(
        {
          vendor_name: inputData.vendorName || '',
          invoice_number: inputData.invoiceNo || '',
          invoice_date: inputData.invoiceDate || '',
          invoice_amount: inputData.invoiceAmount?.toString() || '',
          vat_amount: inputData.vatAmount?.toString() || '',
        } as Record<string, string>,
        externalResponse.results.fields,
        fieldTypes
      );

      const results = {
        ...externalResponse.results,
        fields: enhancedFields,
        summary: enhancedSummary,
        is_colored: colorDetection.is_colored,
        color_pages: colorDetection.color_pages,
        total_pages: colorDetection.total_pages
      };

      // Update the log with the results
      await updateInvoiceLogWithResults(log.id, externalResponse, colorDetection, results);

      // Return the response in the format expected by the client
      return NextResponse.json(createApiResponse(externalResponse, log.id, results));

    } catch (error) {
      // Update the log with the error
      await updateLogWithError(log.id, error);

      // Return error response
      return NextResponse.json(
        createErrorResponse(error, log.id),
        { status: 500 }
      );
    }

  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error processing invoice from URL:', error);
    }
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: errorMessage,
        results: { success: false }
      },
      { status: 500 }
    );
  }
}
