import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse, InvoiceData, TaxInvoiceData } from '@/lib/types/invoice';
import { prisma } from '@/lib/db';
import { detectColor } from '@/lib/api/enhanced-color-detection';
import { ColorDetectionResult } from '@/lib/api/enhanced-color-detection';
import { getPdfPageCount, getPdfPageCountFromUrl } from '@/lib/api/pdf-utils';
import formidable from 'formidable';
import { Readable } from 'stream';

/**
 * Creates a default error response for invoice processing
 * @param error The error that occurred
 * @param logId Optional log ID to include in the response
 * @returns A formatted error response
 */
export function createErrorResponse(error: unknown, logId?: string): ApiResponse {
  const errorMessage = error instanceof Error ? error.message :
                      typeof error === 'string' ? error : 'Unknown error';

  // Create a basic error response with only the required field attributes
  const errorResponse: ApiResponse = {
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: {
        total_fields: 0,
        matched: 0,
        mismatched: 0,
        not_found: 0
      },
      fields: {
        vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
        vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
      }
    },
    error: errorMessage,
  };

  if (logId) {
    errorResponse.log_id = logId;
  }

  return errorResponse;
}

/**
 * Creates a default error response for tax invoice processing
 * @param error The error that occurred
 * @param logId Optional log ID to include in the response
 * @returns A formatted error response
 */
export function createTaxInvoiceErrorResponse(error: unknown, logId?: string): ApiResponse {
  const errorMessage = error instanceof Error ? error.message :
                      typeof error === 'string' ? error : 'Unknown error';

  // Create a basic error response with only the required field attributes
  const errorResponse: ApiResponse = {
    processing_id: `error-${Date.now()}`,
    results: {
      success: false,
      summary: {
        total_fields: 0,
        matched: 0,
        mismatched: 0,
        not_found: 0
      },
      fields: {
        vendor_name: { input_value: '', ocr_value: '', status: 'not_found' },
        tax_invoice_number: { input_value: '', ocr_value: '', status: 'not_found' },
        tax_invoice_date: { input_value: '', ocr_value: '', status: 'not_found' },
        invoice_amount: { input_value: '', ocr_value: '', status: 'not_found' },
        vat_amount: { input_value: '', ocr_value: '', status: 'not_found' }
      }
    },
    error: errorMessage,
  };

  if (logId) {
    errorResponse.log_id = logId;
  }

  return errorResponse;
}

/**
 * Parses a date string into a Date object or returns null if invalid
 * @param dateStr The date string to parse
 * @returns A Date object or null
 */
export function parseDate(dateStr: string): Date | null {
  try {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    return null;
  }
}

/**
 * Parses an OCR date from the results
 * @param dateValue The date string from OCR results
 * @returns A Date object or null if invalid
 */
export function parseOcrDate(dateValue: string): Date | null {
  try {
    const ocrDate = new Date(dateValue);
    if (isNaN(ocrDate.getTime())) {
      return null;
    }
    return ocrDate;
  } catch (error) {
    return null;
  }
}

/**
 * Handles color detection for invoice PDFs
 * @param buffer The PDF file as a buffer
 * @returns Color detection results or default values if detection fails
 */
export async function handleColorDetection(buffer: Buffer) {
  try {
    const colorDetection = await detectColor(buffer);
    return colorDetection;
  } catch (colorError) {
    console.error("Error in color detection:", colorError);
    // Provide default values if color detection fails
    return {
      is_colored: false,
      color_pages: [],
      total_pages: 0,
      confidence: 'low',
      method: 'Failed'
    };
  }
}

/**
 * Creates a log entry for invoice processing
 * @param requestType The type of request ('invoice' or 'taxInvoice')
 * @param fileName The name of the file being processed
 * @param fileSize The size of the file in bytes
 * @param inputData The input data for matching
 * @param apiKeyId Optional API key ID used for the request
 * @returns The created log entry
 */
export async function createProcessingLog(
  requestType: 'invoice' | 'taxInvoice',
  fileName: string,
  fileSize: number,
  inputData: InvoiceData | TaxInvoiceData,
  apiKeyId?: string
) {
  // Parse the document date
  let documentDate: Date | null = null;
  if (inputData.invoiceDate) {
    documentDate = typeof inputData.invoiceDate === 'string'
      ? parseDate(inputData.invoiceDate)
      : inputData.invoiceDate;
  }

  // Create a log entry
  const log = await prisma.processingLog.create({
    data: {
      requestType,
      fileName,
      fileSize,
      processingStatus: 'processing',
      // Store input values in the fields
      inputVendorName: inputData.vendorName,
      inputDocumentNo: requestType === 'invoice'
        ? (inputData as InvoiceData).invoiceNo
        : (inputData as TaxInvoiceData).taxInvoiceNo,
      inputDocumentDate: documentDate,
      inputInvoiceAmount: inputData.invoiceAmount,
      inputVatAmount: inputData.vatAmount,
    },
  });

  // Verify if the API key ID was saved correctly
  if (apiKeyId) {
    const savedLog = await prisma.processingLog.findUnique({
      where: { id: log.id },
      select: { apiKeyId: true }
    });
  }

  return log;
}

/**
 * Creates a log entry for URL-based invoice processing
 * @param requestType The type of request ('invoice' or 'taxInvoice')
 * @param fileUrl The URL of the file being processed
 * @param inputData The input data for matching
 * @param apiKeyId Optional API key ID used for the request
 * @returns The created log entry
 */
export async function createUrlProcessingLog(
  requestType: 'invoice' | 'taxInvoice',
  fileUrl: string,
  inputData: InvoiceData | TaxInvoiceData,
  apiKeyId?: string
) {
  // Parse the document date
  let documentDate: Date | null = null;
  if (inputData.invoiceDate) {
    documentDate = typeof inputData.invoiceDate === 'string'
      ? parseDate(inputData.invoiceDate)
      : inputData.invoiceDate;
  }

  // Extract filename from URL if possible
  const fileName = fileUrl.split('/').pop() || 'remote-file.pdf';

  // Create a log entry
  const log = await prisma.processingLog.create({
    data: {
      requestType,
      fileName,
      fileSize: 0, // We don't know the file size for URL-based processing
      processingStatus: 'processing',
      // Store input values in the fields
      inputVendorName: inputData.vendorName,
      inputDocumentNo: requestType === 'invoice'
        ? (inputData as InvoiceData).invoiceNo
        : (inputData as TaxInvoiceData).taxInvoiceNo,
      inputDocumentDate: documentDate,
      inputInvoiceAmount: inputData.invoiceAmount,
      inputVatAmount: inputData.vatAmount,
    },
  });

  // Verify if the API key ID was saved correctly
  if (apiKeyId) {
    const savedLog = await prisma.processingLog.findUnique({
      where: { id: log.id },
      select: { apiKeyId: true }
    });
  }

  return log;
}

/**
 * Updates a log entry with error information
 * @param logId The ID of the log entry to update
 * @param error The error that occurred
 */
export async function updateLogWithError(logId: string, error: unknown) {
  await prisma.processingLog.update({
    where: { id: logId },
    data: {
      processingStatus: 'failed',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
    },
  });
}

/**
 * Updates a processing log with invoice results using raw SQL
 * @param logId The ID of the log entry to update
 * @param externalResponse The response from the external service
 * @param colorDetection The color detection results
 * @param results The combined results
 */
export async function updateInvoiceLogWithResults(
  logId: string,
  externalResponse: any,
  colorDetection: ColorDetectionResult,
  results: any
) {
  // Parse OCR date
  const ocrDocumentDate = parseOcrDate(results.fields.invoice_date.ocr_value);

  // Extract summary statistics
  const summary = results.summary;

  // Update the log with the results
  await prisma.$executeRaw`
    UPDATE "processing_logs"
    SET
      "processing_status" = 'success',
      "processing_id" = ${externalResponse.document_processing_log_id},
      "is_colored" = ${colorDetection.is_colored},
      "color_pages" = ${colorDetection.color_pages}::integer[],
      "total_pages" = ${colorDetection.total_pages},
      "matching_result" = ${JSON.stringify(results)}::jsonb,
      "ocr_vendor_name" = ${results.fields.vendor_name.ocr_value},
      "ocr_document_no" = ${results.fields.invoice_number.ocr_value},
      "ocr_document_date" = ${ocrDocumentDate},
      "ocr_invoice_amount" = ${parseFloat(results.fields.invoice_amount.ocr_value)},
      "ocr_vat_amount" = ${parseFloat(results.fields.vat_amount.ocr_value)},
      "total_fields" = ${summary.total_fields},
      "matched_fields" = ${summary.matched},
      "mismatched_fields" = ${summary.mismatched},
      "not_found_fields" = ${summary.not_found},
      "processed_at" = ${new Date()}
    WHERE "id" = ${logId}
  `;
}

/**
 * Updates a processing log with tax invoice results using Prisma client
 * @param logId The ID of the log entry to update
 * @param externalResponse The response from the external service
 * @param results The results
 */
export async function updateTaxInvoiceLogWithResults(
  logId: string,
  externalResponse: any,
  results: any
) {
  // Parse OCR date
  const ocrDocumentDate = parseOcrDate(results.fields.tax_invoice_date.ocr_value);

  // Extract summary statistics
  const summary = results.summary;

  // Create update data object
  const updateData: any = {
    processingStatus: 'success',
    processingId: externalResponse.document_processing_log_id,
    matchingResult: results,
    // Store OCR values in the new fields
    ocrVendorName: results.fields.vendor_name.ocr_value,
    ocrDocumentNo: results.fields.tax_invoice_number.ocr_value,
    ocrDocumentDate: ocrDocumentDate,
    ocrInvoiceAmount: parseFloat(results.fields.invoice_amount.ocr_value),
    ocrVatAmount: parseFloat(results.fields.vat_amount.ocr_value),
    // Statistics
    totalFields: summary.total_fields,
    matchedFields: summary.matched,
    mismatchedFields: summary.mismatched,
    notFoundFields: summary.not_found,
    // Set processedAt to current time
    processedAt: new Date(),
  };

  // Add total_pages if available in the results
  if (results.total_pages !== undefined) {
    updateData.totalPages = results.total_pages;
  }

  // Update the log with the results
  await prisma.processingLog.update({
    where: { id: logId },
    data: updateData,
  });
}

/**
 * Extracts total_pages from the external service response if available
 * @param externalResponse The response from the external service
 * @returns The total_pages value or null if not available
 */
export function extractTotalPages(externalResponse: any): number | null {
  // Check if the external response contains total_pages
  if (externalResponse &&
      externalResponse.results &&
      externalResponse.results.total_pages !== undefined) {
    return externalResponse.results.total_pages;
  }

  return null;
}

/**
 * Gets the total number of pages in a PDF file
 * @param buffer The PDF file as a buffer
 * @returns Promise resolving to the number of pages in the PDF
 */
export async function getTotalPagesFromBuffer(buffer: Buffer): Promise<number> {
  try {
    const pageCount = await getPdfPageCount(buffer);
    return pageCount;
  } catch (error) {
    console.error("Error counting PDF pages from buffer:", error);
    return 0;
  }
}

/**
 * Gets the total number of pages in a PDF file from a URL
 * @param url The URL of the PDF file
 * @returns Promise resolving to the number of pages in the PDF
 */
export async function getTotalPagesFromUrl(url: string): Promise<number> {
  try {
    const pageCount = await getPdfPageCountFromUrl(url);
    return pageCount;
  } catch (error) {
    console.error("Error counting PDF pages from URL:", error);
    return 0;
  }
}

/**
 * Filters field matching results to only include the required attributes
 * @param fields The field matching results to filter
 * @returns Filtered field matching results
 */
export function filterFieldMatchingResults(fields: any): any {
  const filteredFields: any = {};

  // Process each field in the matching results
  for (const [key, field] of Object.entries(fields)) {
    // Only keep the three required attributes for each field
    filteredFields[key] = {
      input_value: (field as any).input_value,
      ocr_value: (field as any).ocr_value,
      status: (field as any).status
    };
  }

  return filteredFields;
}

/**
 * Updates the API key ID for a processing log
 * @param logId The ID of the log entry to update
 * @param apiKeyId The API key ID to set
 */
export async function updateLogWithApiKeyId(logId: string, apiKeyId: string): Promise<void> {
  try {
    // First try using Prisma client update
    await prisma.processingLog.update({
      where: { id: logId },
      data: { apiKeyId }
    });

    // Verify the update
    const updatedLog = await prisma.processingLog.findUnique({
      where: { id: logId },
      select: { apiKeyId: true }
    });

    // If the update didn't work, try with raw SQL as a fallback
    if (!updatedLog?.apiKeyId) {
      await prisma.$executeRaw`
        UPDATE "processing_logs"
        SET "api_key_id" = ${apiKeyId}::uuid
        WHERE "id" = ${logId}::uuid
      `;

      // Verify the raw SQL update
      const updatedLogAfterRawSql = await prisma.processingLog.findUnique({
        where: { id: logId },
        select: { apiKeyId: true }
      });
    }
  } catch (error) {
    console.error('Error updating API key ID:', error);

    // Try with raw SQL as a last resort if Prisma update failed
    try {
      await prisma.$executeRaw`
        UPDATE "processing_logs"
        SET "api_key_id" = ${apiKeyId}::uuid
        WHERE "id" = ${logId}::uuid
      `;

      // Verify the raw SQL update
      const updatedLogAfterRawSql = await prisma.processingLog.findUnique({
        where: { id: logId },
        select: { apiKeyId: true }
      });

    } catch (sqlError) {
      console.error('Error updating API key ID with raw SQL:', sqlError);
    }
  }
}

/**
 * Creates a standard API response
 * @param externalResponse The response from the external service
 * @param logId The ID of the log entry
 * @param results The results to include in the response
 * @returns A formatted API response
 */
export function createApiResponse(
  externalResponse: any,
  logId: string,
  results: any
): ApiResponse {
  // Create a copy of the results to avoid modifying the original
  const filteredResults = { ...results };

  // Filter the fields to only include the required attributes
  if (filteredResults.fields) {
    filteredResults.fields = filterFieldMatchingResults(filteredResults.fields);
  }

  return {
    processing_id: externalResponse.processing_id,
    results: filteredResults,
    log_id: logId,
  };
}

/**
 * Parses multipart form data using Formidable library
 * @param req The NextRequest object
 * @returns Promise resolving to parsed fields and files
 */
export async function parseFormDataWithFormidable(req: NextRequest): Promise<{
  fields: Record<string, string>;
  files: Record<string, { buffer: Buffer; filename: string; mimetype: string; size: number }>;
}> {
  return new Promise(async (resolve, reject) => {
    try {
      // Check content type
      const contentType = req.headers.get('content-type');

      if (!contentType || !contentType.includes('multipart/form-data')) {
        console.error('Invalid content type:', contentType);
        throw new Error(`Invalid content type: ${contentType}. Expected multipart/form-data.`);
      }

      // Get the raw body as buffer
      const arrayBuffer = await req.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Create a readable stream from the buffer
      const stream = new Readable({
        read() {
          this.push(buffer);
          this.push(null); // End the stream
        }
      });

      // Set up headers for the stream
      (stream as any).headers = Object.fromEntries(req.headers.entries());

      // Create formidable instance
      const form = formidable({
        maxFileSize: 50 * 1024 * 1024, // 50MB limit
        allowEmptyFiles: false,
        keepExtensions: true,
      });

      // Parse the form data
      form.parse(stream as any, (err, fields, files) => {
        if (err) {
          console.error('Formidable parsing error:', err);
          reject(new Error(`Formidable parsing failed: ${err.message}`));
          return;
        }

        try {
          // Convert fields to simple string values
          const parsedFields: Record<string, string> = {};
          for (const [key, value] of Object.entries(fields)) {
            if (Array.isArray(value)) {
              parsedFields[key] = value[0] || '';
            } else {
              parsedFields[key] = value || '';
            }
          }

          // Convert files to buffer-based objects
          const parsedFiles: Record<string, { buffer: Buffer; filename: string; mimetype: string; size: number }> = {};
          for (const [key, value] of Object.entries(files)) {
            let file: formidable.File | undefined;
            if (Array.isArray(value) && value.length > 0) {
              file = value[0];
            } else if (value && !Array.isArray(value)) {
              file = value;
            }

            if (file && file.filepath) {
              // Read the file content into a buffer
              const fs = require('fs');
              const fileBuffer = fs.readFileSync(file.filepath);

              parsedFiles[key] = {
                buffer: fileBuffer,
                filename: file.originalFilename || 'unknown',
                mimetype: file.mimetype || 'application/octet-stream',
                size: file.size
              };

              // Clean up the temporary file
              try {
                fs.unlinkSync(file.filepath);
              } catch (cleanupError) {
                console.warn('Failed to cleanup temp file:', cleanupError);
              }
            } else {
              console.log(`File ${key} has no filepath or is invalid`);
            }
          }
          resolve({ fields: parsedFields, files: parsedFiles });
        } catch (processingError) {
          reject(new Error(`Error processing parsed data: ${processingError}`));
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Form data parsing error:', {
        error: errorMessage,
        contentType: req.headers.get('content-type'),
        method: req.method,
        url: req.url
      });
      reject(new Error(`Failed to parse multipart form data: ${errorMessage}`));
    }
  });
}
